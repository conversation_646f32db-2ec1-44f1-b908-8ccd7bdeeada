#!/usr/bin/env node

/**
 * Simple test to verify server starts correctly with proper transport logging
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testServerStartup() {
  console.log('🧪 Testing MCP Server Startup...\n');

  const serverPath = path.join(__dirname, 'dist', 'index.js');
  
  return new Promise((resolve, reject) => {
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname
    });

    let output = '';
    let hasTransportInfo = false;
    let hasStdioTransport = false;

    server.stderr.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      // Check for transport information
      if (text.includes('"transport":"stdio"')) {
        hasStdioTransport = true;
      }
      
      if (text.includes('"transportType":"stdin/stdout"')) {
        hasTransportInfo = true;
      }
      
      // Check if server started successfully
      if (text.includes('Server started successfully') && hasStdioTransport && hasTransportInfo) {
        console.log('✅ Server started with correct transport configuration');
        console.log('✅ Transport: stdio (stdin/stdout)');
        console.log('✅ No misleading port/host information');
        
        server.kill();
        resolve(true);
      }
    });

    server.on('error', (error) => {
      reject(new Error(`Failed to start server: ${error.message}`));
    });

    // Timeout after 15 seconds (server takes time to initialize all detectors)
    setTimeout(() => {
      server.kill();
      if (!hasStdioTransport || !hasTransportInfo) {
        console.log('\n📋 Server output so far:');
        console.log(output);
        reject(new Error('Server did not start with correct transport configuration'));
      } else {
        reject(new Error('Server startup timeout'));
      }
    }, 15000);
  });
}

// Run the test
testServerStartup()
  .then(() => {
    console.log('\n🎉 Server startup test passed!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Server starts successfully');
    console.log('- ✅ Uses stdio transport (not network ports)');
    console.log('- ✅ Logs clear transport information');
    console.log('- ✅ No misleading port/host configuration');
    console.log('\n🔧 Next Steps:');
    console.log('1. Configure your IDE to use this MCP server');
    console.log('2. Use the path: /Volumes/Storage/Tool_Projects/error-debugging-mcp-server/dist/index.js');
    console.log('3. Ensure transport is set to "stdio" in IDE configuration');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Server startup test failed:', error.message);
    process.exit(1);
  });
