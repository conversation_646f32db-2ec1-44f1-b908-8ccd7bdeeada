name: Release

on:
  push:
    tags:
      - 'v*'

env:
  NODE_VERSION: '18'

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    
    permissions:
      contents: write
      packages: write
    
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      version: ${{ steps.get_version.outputs.version }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Get version from tag
      id: get_version
      run: echo "version=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
      
    - name: Generate changelog
      id: changelog
      run: |
        # Generate changelog from git commits
        PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
        if [ -z "$PREVIOUS_TAG" ]; then
          CHANGELOG=$(git log --pretty=format:"- %s" --no-merges)
        else
          CHANGELOG=$(git log --pretty=format:"- %s" --no-merges ${PREVIOUS_TAG}..HEAD)
        fi
        echo "changelog<<EOF" >> $GITHUB_OUTPUT
        echo "$CHANGELOG" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
        
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release v${{ steps.get_version.outputs.version }}
        body: |
          ## Changes in v${{ steps.get_version.outputs.version }}
          
          ${{ steps.changelog.outputs.changelog }}
          
          ## Installation
          
          ```bash
          npm install -g error-debugging-mcp-server@${{ steps.get_version.outputs.version }}
          ```
          
          ## Docker
          
          ```bash
          docker pull ghcr.io/${{ github.repository }}:v${{ steps.get_version.outputs.version }}
          ```
          
          ## Documentation
          
          - [API Documentation](https://github.com/${{ github.repository }}/blob/v${{ steps.get_version.outputs.version }}/docs/API.md)
          - [User Guide](https://github.com/${{ github.repository }}/blob/v${{ steps.get_version.outputs.version }}/docs/USER_GUIDE.md)
          - [Examples](https://github.com/${{ github.repository }}/blob/v${{ steps.get_version.outputs.version }}/docs/EXAMPLES.md)
        draft: false
        prerelease: ${{ contains(steps.get_version.outputs.version, '-') }}

  build-assets:
    name: Build Release Assets
    runs-on: ubuntu-latest
    needs: create-release
    
    strategy:
      matrix:
        os: [linux, darwin, win32]
        arch: [x64, arm64]
        exclude:
          - os: win32
            arch: arm64
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Package application
      run: |
        mkdir -p release
        cp -r dist package.json README.md LICENSE docs release/
        cd release
        tar -czf ../error-debugging-mcp-server-v${{ needs.create-release.outputs.version }}-${{ matrix.os }}-${{ matrix.arch }}.tar.gz .
        
    - name: Upload Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: ./error-debugging-mcp-server-v${{ needs.create-release.outputs.version }}-${{ matrix.os }}-${{ matrix.arch }}.tar.gz
        asset_name: error-debugging-mcp-server-v${{ needs.create-release.outputs.version }}-${{ matrix.os }}-${{ matrix.arch }}.tar.gz
        asset_content_type: application/gzip

  update-documentation:
    name: Update Documentation
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Update version in documentation
      run: |
        # Update version references in documentation
        sed -i "s/version: '[^']*'/version: '${{ needs.create-release.outputs.version }}'/g" docs/*.md
        sed -i "s/@[0-9]\+\.[0-9]\+\.[0-9]\+/@${{ needs.create-release.outputs.version }}/g" docs/*.md
        
    - name: Commit documentation updates
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add docs/
        git diff --staged --quiet || git commit -m "docs: update version to v${{ needs.create-release.outputs.version }}"
        git push

  notify-release:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [create-release, build-assets, update-documentation]
    if: always()
    
    steps:
    - name: Notify success
      if: ${{ needs.create-release.result == 'success' && needs.build-assets.result == 'success' }}
      run: |
        echo "🎉 Release v${{ needs.create-release.outputs.version }} created successfully!"
        echo "📦 Assets built and uploaded"
        echo "📚 Documentation updated"
        
    - name: Notify failure
      if: ${{ needs.create-release.result == 'failure' || needs.build-assets.result == 'failure' }}
      run: |
        echo "❌ Release process failed. Please check the logs."
