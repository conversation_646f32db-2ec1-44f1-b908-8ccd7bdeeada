{"name": "error-debugging-mcp-server", "version": "1.0.0", "description": "A production-ready Model Context Protocol (MCP) server for AI-powered error detection, debugging, and performance monitoring across multiple programming languages", "main": "dist/index.js", "exports": {".": "./dist/exports.js", "./server": "./dist/index.js"}, "type": "module", "scripts": {"build": "tsc && npx tsc-alias", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,json}", "typecheck": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build", "benchmark": "tsx benchmarks/run.ts", "benchmark:ci": "tsx benchmarks/run.ts --ci", "optimize": "npm run build && npm run benchmark", "cleanup": "scripts/cleanup.sh", "cleanup:deep": "scripts/cleanup.sh --deep", "test:mcp": "node scripts/test-mcp-protocol.js", "test:integration": "node scripts/test-mcp-integration.js", "test:errors": "node scripts/simple-mcp-test.js", "verify": "npm run build && npm run test && npm run test:mcp"}, "keywords": ["mcp", "model-context-protocol", "debugging", "error-detection", "ai-ide", "typescript", "javascript", "python", "go", "rust", "eslint", "linting", "performance-monitoring", "debug-session", "breakpoints", "variable-inspection", "call-stack", "profiling", "metrics", "vscode", "cursor", "windsurf", "augment-code", "ide-integration", "real-time-monitoring", "multi-language", "production-ready"], "author": "Error Debugging MCP Server", "license": "MIT", "homepage": "https://github.com/your-org/error-debugging-mcp-server#readme", "repository": {"type": "git", "url": "git+https://github.com/your-org/error-debugging-mcp-server.git"}, "bugs": {"url": "https://github.com/your-org/error-debugging-mcp-server/issues"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "files": ["dist", "docs", "README.md", "LICENSE", "package.json"], "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "acorn": "^8.11.3", "acorn-walk": "^8.3.2", "chalk": "^5.3.0", "chokidar": "^3.5.3", "commander": "^11.1.0", "eslint": "^8.57.1", "fast-glob": "^3.3.2", "node-fetch": "^3.3.2", "ora": "^8.0.1", "semver": "^7.5.4", "source-map": "^0.7.4", "typescript": "^5.8.3", "ws": "^8.16.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.6", "@types/semver": "^7.5.6", "@types/source-map": "^0.5.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "@vitest/coverage-v8": "^1.1.3", "@vitest/ui": "^1.1.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "prettier": "^3.1.1", "rimraf": "^5.0.5", "tsx": "^4.7.0", "vitest": "^1.1.3"}}