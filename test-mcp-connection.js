#!/usr/bin/env node

/**
 * Simple test script to verify MCP server connection and functionality
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class MCPTester {
  constructor() {
    this.server = null;
    this.requestId = 1;
  }

  async testServer() {
    console.log('🧪 Testing MCP Server Connection...\n');

    try {
      await this.startServer();
      await this.testInitialize();
      await this.testListTools();
      await this.testDetectErrors();
      console.log('\n✅ All tests passed! MCP server is working correctly.');
    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    } finally {
      if (this.server) {
        this.server.kill();
      }
    }
  }

  async startServer() {
    console.log('🚀 Starting MCP server...');
    
    const serverPath = path.join(__dirname, 'dist', 'index.js');
    this.server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname
    });

    this.server.stderr.on('data', (data) => {
      console.log('📋 Server log:', data.toString().trim());
    });

    // Wait for server to start
    await new Promise(resolve => setTimeout(resolve, 3000));
    console.log('✅ Server started\n');
  }

  async sendRequest(method, params = {}) {
    const request = {
      jsonrpc: '2.0',
      id: this.requestId++,
      method,
      params
    };

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Request timeout for ${method}`));
      }, 5000);

      this.server.stdout.once('data', (data) => {
        clearTimeout(timeout);
        try {
          const response = JSON.parse(data.toString());
          if (response.error) {
            reject(new Error(`${method} failed: ${response.error.message}`));
          } else {
            resolve(response.result);
          }
        } catch (err) {
          reject(new Error(`Invalid JSON response: ${data.toString()}`));
        }
      });

      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async testInitialize() {
    console.log('🔧 Testing initialization...');
    
    const result = await this.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {
        roots: {
          listChanged: true
        }
      },
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    });

    console.log('✅ Initialize successful:', result.serverInfo?.name);
  }

  async testListTools() {
    console.log('🛠️  Testing list tools...');
    
    const result = await this.sendRequest('tools/list');
    console.log('✅ Available tools:', result.tools?.map(t => t.name).join(', '));
  }

  async testDetectErrors() {
    console.log('🔍 Testing detect-errors tool...');
    
    const result = await this.sendRequest('tools/call', {
      name: 'detect-errors',
      arguments: {
        source: 'all',
        includeWarnings: true,
        realTime: false
      }
    });

    console.log('✅ Detect errors completed:', result.content?.[0]?.text?.substring(0, 100) + '...');
  }
}

// Run the test
const tester = new MCPTester();
tester.testServer().catch(console.error);
