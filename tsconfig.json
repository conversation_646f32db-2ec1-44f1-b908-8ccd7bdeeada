{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/server/*": ["src/server/*"], "@/detectors/*": ["src/detectors/*"], "@/analyzers/*": ["src/analyzers/*"], "@/debuggers/*": ["src/debuggers/*"], "@/diagnostics/*": ["src/diagnostics/*"], "@/integrations/*": ["src/integrations/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "benchmarks"]}