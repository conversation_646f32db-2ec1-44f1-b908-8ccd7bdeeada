# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Compressed files
*.zip
*.rar
*.7z
*.gz
*.tar
*.tar.gz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.*
!.env.example

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Sveltekit cache directory
.svelte-kit/

# vitepress build output
**/.vitepress/dist

# vitepress cache directory
**/.vitepress/cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Firebase cache directory
.firebase/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v3
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Vite logs files
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# TypeScript build output
dist/
build/
lib/

# Test coverage reports (additional patterns)
*.lcov.info

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# MCP Server specific
*.mcp
mcp-server.log
error-debugging-config.json
error-debugging-mcp-server.zip

# Development and testing
test-project/
benchmarks/results/
*.benchmark.json

# Docker volumes and data
docker-data/
.docker/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Backup files
*.backup
*.bak
*.orig

# Runtime configuration
config/local.json
config/development.json
config/staging.json
configs/local/
configs/development/
configs/staging/

# Logs and debugging
*.log.*
debug.log
error.log
access.log
combined.log

# Package manager files
package-lock.json.backup
yarn.lock.backup
.pnpm-debug.log

# Environment specific
.env.local
.env.development
.env.staging
.env.production

# Performance and profiling
*.cpuprofile
*.heapprofile
*.heapsnapshot

# Security
.secrets
secrets/
private/
*.key
*.pem
*.p12
*.pfx

# Documentation build
docs/build/
docs/dist/
.docusaurus/

# Additional miscellaneous
*.tgz.backup

# MCP Protocol specific
*.mcp.json
mcp-*.log
mcp-debug-*
.mcp-cache/

# Development tools
.history/
.vscode-test/
.nyc_output/

# Build artifacts
*.d.ts.map
*.js.map

# Test artifacts
test-results/
test-output/
*.test.log
